import type { ActionFunctionArgs, TypedResponse } from "@remix-run/node";
import { json, redirect } from "@remix-run/node";
import { getCurrentStep, getNextStep } from "../hooks/use-form-steps";
import type { FinancialPeriodSchemaType } from "../types/bahamas/financial-period-schema";
import { Pages } from "./form-pages-bahamas";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { commitSession, getSession } from "~/lib/auth/utils/session.server";
import { middleware } from "~/lib/middlewares.server";
import { getFlattenedSubmission, getUnflattenedDataSet } from "~/lib/submission/utilities/submission-data-set-auto";
import { SubmissionStatusNames } from "~/lib/submission/utilities/submission-status";
import {
  clientGetSubmission,
  clientPutSubmissionDataSet,
  clientSubmitSubmission,
  clientUpdateSubmissionInformation,
} from "~/services/api-generated";
import { decodeFields } from "~/lib/utilities/hashPrefix";

export async function getFormAction({
  request,
  params,
}: ActionFunctionArgs, page: string): Promise<TypedResponse<null> | undefined> {
  const { company } = await middleware(["auth", "mfa", "terms", "requireEsModule", "requireCompany"], request);
  const session = await getSession(request.headers.get("Cookie"));
  const { id } = params;

  if (!id) {
    throw new Error("Submission ID is required");
  }

  const formData = await request.formData();
  const data = JSON.parse(formData.get("data") as string)
  /*
   * Since undefined values are sent as the string "undefined" (due to JSON.stringify on the client side),
   * we need to convert them back to undefined while reconstructing the data object.
   */
  const newData = decodeFields(Object.fromEntries(
    Object.entries(data).map(([key, value]) => {
      return [key, value === "undefined" ? undefined : value];
    }),
  ));
  const { data: submission } = await clientGetSubmission({
    headers: await authHeaders(request),
    path: { submissionId: id },
    query: { includeFormDocument: true },
  });

  if (!submission) {
    throw new Error("Submission not found")
  }

  const currentStep = getCurrentStep(page, company.jurisdictionName);
  if (!currentStep) {
    throw new Error("Current step is not available");
  }

  const unflattenedData = getUnflattenedDataSet(submission);
  // Update the submission dataset with the new data but keep the other pages intact
  const unflattenedNewSubmission = {
    ...unflattenedData,
    [page]: {
      ...unflattenedData[page],
      ...newData,
    },
  };
  const newSubmissionData = getFlattenedSubmission(unflattenedNewSubmission);

  try {
    await clientPutSubmissionDataSet({
      headers: await authHeaders(request),
      path: { submissionId: id },
      body: { id, dataSet: newSubmissionData, documentIds: submission.documentIds },
    })
    if (page === Pages.FINANCIAL_PERIOD && (submission.status === SubmissionStatusNames.Temporal || submission.status === SubmissionStatusNames.Draft || submission.status === SubmissionStatusNames.Revision)) {
      const { startDate, endDate } = unflattenedNewSubmission[Pages.FINANCIAL_PERIOD] as FinancialPeriodSchemaType
      if (!startDate || !endDate) {
        throw new Error("Start date and end date are mandatory for update the submission information")
      }

      const { error } = await clientUpdateSubmissionInformation({
        headers: await authHeaders(request),
        path: { submissionId: id },
        body: {
          startAt: startDate.toString(), // This is already in UTC timezone,
          endAt: endDate.toString(), // This is already in UTC timezone,
        },
      })

      if (error) {
        session.flash("notification", {
          title: "An error has occurred",
          message: error.exceptionMessage,
          variant: "error",
        });

        return json(null, { status: 500, headers: { "Set-Cookie": await commitSession(session) } });
      }
    }

    if (page === Pages.FINALIZE) {
      if (submission.status !== SubmissionStatusNames.Draft && submission.status !== SubmissionStatusNames.Revision && submission.status !== SubmissionStatusNames.Temporal) {
        session.flash("notification", {
          title: "Error!",
          message: "Submission was already submitted",
          variant: "error",
        });

        return redirect("/economic-substance/new", { headers: { "Set-Cookie": await commitSession(session) } });
      }

      // Finalize the submission
      const { error } = await clientSubmitSubmission({
        headers: await authHeaders(request),
        path: { submissionId: id },
        body: { submissionId: id },
      });
      if (error) {
        session.flash("notification", {
          title: "An Error has occurred",
          message: error.exceptionMessage,
          variant: "error",
        });

        return json(null, { status: 500, headers: { "Set-Cookie": await commitSession(session) } });
      }

      return redirect(`/economic-substance/${params.id}/confirmation`);
    }

    const nextPage = getNextStep(unflattenedNewSubmission, page, company.jurisdictionName);

    if (nextPage) {
      return redirect(`/economic-substance/${params.id}/${nextPage}`);
    }
  } catch (error) {
    if (error instanceof Response) {
      const errorMessage = await error.text();

      if (errorMessage) {
        session.flash("notification", {
          title: "An Error has occurred",
          message: errorMessage,
          variant: "error",
        });

        return json(null, { status: error.status, headers: { "Set-Cookie": await commitSession(session) } });
      }
    } else {
      throw error;
    }
  }
}
