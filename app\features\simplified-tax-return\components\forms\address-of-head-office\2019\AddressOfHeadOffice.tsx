import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { Combobox, Form, FormControl, FormField, FormItem, FormLabel, FormMessage, Input, RadioGroup, RadioGroupItem } from "@netpro/design-system";
import { Form as RemixForm, useFetcher } from "@remix-run/react";
import { useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import type { AddressOfHeadOfficeType } from "~/lib/simplified-tax-return/types/address-of-head-office/2019/address-of-head-office-schema";
import { addressOfHeadOfficeSchema } from "~/lib/simplified-tax-return/types/address-of-head-office/2019/address-of-head-office-schema";
import { Pages } from "~/lib/simplified-tax-return/utilities/form-pages";
import { useSubmission } from "~/lib/submission/context/use-submission";
import { getCountryOptions } from "~/lib/utilities/countries";
import { decodeFields, encodeFields } from "~/lib/utilities/hashPrefix";
import { useScrollToError } from "~/lib/utilities/use-scroll-to-error";

export function AddressOfHeadOffice(): JSX.Element {
  const { submissionData } = useSubmission();
  const data = useMemo(() => {
    const raw = submissionData[Pages.ADDRESS_OF_HEAD_OFFICE] as AddressOfHeadOfficeType;
    const filled: AddressOfHeadOfficeType = {
      address1: raw?.address1 ?? "",
      address2: raw?.address2 ?? "",
      nevisAddress1: raw?.nevisAddress1 ?? "",
      nevisAddress2: raw?.nevisAddress2 ?? "",
      city: raw?.city ?? "",
      zipCode: raw?.zipCode ?? "",
      country: raw?.country ?? "",
      isAddressInNevisDifferent: raw?.isAddressInNevisDifferent ?? "true",
    };

    return decodeFields(filled, ["address1", "address2", "nevisAddress1", "nevisAddress2"]);
  }, [submissionData]);
  const form = useForm<AddressOfHeadOfficeType>({
    resolver: zodResolver(addressOfHeadOfficeSchema),
    shouldFocusError: false,
    defaultValues: {
      address1: "",
      address2: "",
      city: "",
      zipCode: "",
      country: "",
      nevisAddress1: "Trident Trust Company (Nevis) Limited",
      nevisAddress2: "Suite 556, Hunkins Waterfront Plaza",
      nevisCity: "Charlestown",
      nevisZipCode: "KN0802",
      nevisCountry: "KNA",
    },
  });
  const { reset, formState, setValue } = form;
  const [canFocus, setCanFocus] = useState(true)
  useScrollToError(formState, canFocus, setCanFocus);

  function onError(): void {
    setCanFocus(true)
  }

  useEffect(() => {
    reset(data, { keepDefaultValues: true });
  }, [data, reset]);

  const countryOptions = useMemo(() => getCountryOptions(), []);
  const fetcher = useFetcher();

  function onSubmit(data: AddressOfHeadOfficeType): void {
    const encoded = encodeFields(data, ["address1", "address2", "nevisAddress1", "nevisAddress2"]);

    fetcher.submit({ data: JSON.stringify(encoded) }, {
      method: "post",
    });
  }

  const isAddressInNevisDifferent = form.watch("isAddressInNevisDifferent");

  useEffect(() => {
    if (isAddressInNevisDifferent === "true") {
      setValue("nevisAddress1", "Trident Trust Company (Nevis) Limited");
      setValue("nevisAddress2", "Suite 556, Hunkins Waterfront Plaza");
      setValue("nevisCity", "Charlestown");
      setValue("nevisZipCode", "KN0802");
    }
  }, [isAddressInNevisDifferent, setValue]);

  return (
    <Form {...form}>
      <RemixForm onSubmit={form.handleSubmit(onSubmit, onError)} noValidate id="str-form">
        <div className="pb-4">
          <p className="max-w-2xl">
            Address of Head Office refers to the headquarters of the Registered Entity.
            This is typically the principal place of business, the address where official
            decisions are recorded, or the location of the company's directors.
          </p>
        </div>
        <div className="flex-col space-y-2">
          <FormField
            control={form.control}
            name="address1"
            render={({ field, fieldState }) => (
              <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                <FormLabel>Address #1 *</FormLabel>
                <FormControl>
                  <Input
                    invalid={!!fieldState.error}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="address2"
            render={({ field, fieldState }) => (
              <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                <FormLabel>Address #2</FormLabel>
                <FormControl>
                  <Input
                    invalid={!!fieldState.error}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="city"
            render={({ field, fieldState }) => (
              <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                <FormLabel>City *</FormLabel>
                <FormControl>
                  <Input
                    invalid={!!fieldState.error}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="zipCode"
            render={({ field, fieldState }) => (
              <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                <FormLabel>Zip code *</FormLabel>
                <FormControl>
                  <Input
                    invalid={!!fieldState.error}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="country"
            render={({ field, fieldState }) => (
              <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                <FormLabel>Country*</FormLabel>
                <FormControl>
                  <Combobox
                    placeholder="Select a country"
                    searchText="Search..."
                    noResultsText="No countries found."
                    items={countryOptions}
                    onChange={field.onChange}
                    value={field.value}
                    invalid={!!fieldState.error}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="isAddressInNevisDifferent"
            render={({ field, fieldState }) => (
              <FormItem className="space-y-3">
                <FormLabel>Is the entity's Registered Address the offices of Trident Nevis? *</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    value={field.value}
                    invalid={!!fieldState.error}
                    className="flex flex-row space-x-2"
                  >
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="true" />
                      </FormControl>
                      <FormLabel className="font-normal">
                        Yes
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="false" />
                      </FormControl>
                      <FormLabel className="font-normal">
                        No
                      </FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          {isAddressInNevisDifferent !== undefined && (
            <>
              <FormField
                control={form.control}
                name="nevisAddress1"
                render={({ field, fieldState }) => (
                  <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                    <FormLabel>Address #1 *</FormLabel>
                    <FormControl>
                      <Input
                        invalid={!!fieldState.error}
                        disabled={isAddressInNevisDifferent === "true"}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="nevisAddress2"
                render={({ field, fieldState }) => (
                  <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                    <FormLabel>Address #2</FormLabel>
                    <FormControl>
                      <Input
                        invalid={!!fieldState.error}
                        disabled={isAddressInNevisDifferent === "true"}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="nevisCity"
                render={({ field, fieldState }) => (
                  <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                    <FormLabel>City *</FormLabel>
                    <FormControl>
                      <Input
                        invalid={!!fieldState.error}
                        disabled={isAddressInNevisDifferent === "true"}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="nevisZipCode"
                render={({ field, fieldState }) => (
                  <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                    <FormLabel>Zip code *</FormLabel>
                    <FormControl>
                      <Input
                        invalid={!!fieldState.error}
                        disabled={isAddressInNevisDifferent === "true"}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="nevisCountry"
                render={() => (
                  <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                    <FormLabel>Country*</FormLabel>
                    <FormControl>
                      <>
                        <Input type="hidden" name="nevisCountry" value="KNA" />
                        <Input type="text" value="Saint Kitts and Nevis" disabled />
                      </>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </>
          )}
        </div>
      </RemixForm>
    </Form>
  )
}
