import { format, isValid, parseISO } from "date-fns";
import {
  format as formatTz,
  toZonedTime,
} from "date-fns-tz";

export type Timezone = "UTC" | "Local" | "Nevis" | "Bahamas" | "Panama";

const TZ_MAP: Record<Timezone, string> = {
  UTC: "UTC",
  Local: "Local",
  Nevis: "America/St_Kitts",
  Bahamas: "America/Nassau",
  Panama: "America/Panama",
};

/**
 * Maps company jurisdiction names to timezone values for date formatting
 */
export function getTimezoneFromJurisdiction(jurisdictionName?: string): Timezone {
  if (!jurisdictionName) {
    return "UTC";
  }

  switch (jurisdictionName.toLowerCase()) {
    case "nevis":
      return "Nevis";
    case "bahamas":
      return "Bahamas";
    case "panama":
      return "Panama";
    default:
      return "UTC";
  }
}

export const dayMonthYearFormat = "dd MMMM yyyy";
export const dashSeparatedDateFormat = "dd-MM-yyyy";
export const shortMonthFormat = "dd-MMM-yyyy";
/*
 * Note: Uncomment the following line to include the hour in the default format, this helps for debugging formatting issues
 * const DEFAULT_FORMAT = `${shortMonthFormat} HH:mm`; // TODO: Default should be just shortMonthFormat. Hour is added for debugging purposes
 */
export const DEFAULT_FORMAT = shortMonthFormat;

/**
 * Formats a UTC date/string into the given timezone & pattern, with fallback for invalid/missing inputs.
 *
 * This function replaces various date formatting utilities by providing safe parsing, timezone conversion,
 * customizable formats, and fallback handling. It assumes inputs are in UTC (appending 'Z' or 'T00:00:00Z'
 * for ISO strings without timezone info). Works consistently on client/server (e.g., Remix.js).
 *
 * @param input - UTC date as ISO string (with or without 'Z'/'T'), Date object, or null/undefined/empty.
 * @param options - Optional configuration.
 * @param options.timezone - 'UTC' | 'Nevis' | 'Bahamas' (default 'UTC').
 * @param options.formatStr - date-fns format string (default 'yyyy-MM-dd HH:mm').
 * @param options.fallbackMessage - String to return for invalid/missing dates (default 'N/A').
 * @returns Formatted date string or fallbackMessage.
 *
 * Examples:
 * - formatDate('2025-07-09T14:00:00') → '2025-07-09 14:00' (UTC default)
 * - formatDate('2025-07-09', { timezone: 'Nevis', formatStr: 'dd-MMM-yyyy' }) → '09-Jul-2025' (in Nevis TZ)
 * - formatDate(null) → 'N/A'
 * - formatDate(new Date('invalid')) → 'N/A'
 */
export function formatDate(
  input: string | Date | null | undefined,
  options?: {
    timezone?: Timezone
    formatStr?: string
    fallbackMessage?: string
  },
): string {
  const {
    timezone = "UTC",
    formatStr = DEFAULT_FORMAT,
    fallbackMessage = "N/A",
  } = options ?? {};

  // Handle missing/invalid inputs early
  if (input == null || (typeof input === "string" && input.trim() === "")) {
    return fallbackMessage;
  }

  let utcDate: Date;

  if (typeof input === "string") {
    let iso = input;

    // If no timezone offset or 'Z' is present, assume UTC and normalize
    if (!/Z|[+-]\d{2}(?::?\d{2})?$/i.test(input)) {
      if (/^\d{4}-\d{2}-\d{2}$/.test(input)) {
        // Date-only ISO (e.g., '2025-07-09') → append 'T00:00:00Z' for UTC midnight
        iso = `${input}T00:00:00Z`;
      } else if (/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?$/.test(input)) {
        // Datetime ISO without 'Z' (e.g., '2025-07-09T14:00:00') → append 'Z'
        iso = `${input}Z`;
      }
      // Non-ISO strings will likely fail parsing and trigger fallback
    }

    try {
      utcDate = parseISO(iso);
      if (!isValid(utcDate)) {
        return fallbackMessage;
      }
    } catch {
      return fallbackMessage;
    }
  } else {
    utcDate = input;
    if (!isValid(utcDate)) {
      return fallbackMessage;
    }
  }

  // Return local browser time if requested
  if (timezone === "Local") {
    return format(utcDate, formatStr);
  }

  // Convert UTC Date to the target timezone
  const tz = TZ_MAP[timezone];
  const zonedDt = toZonedTime(utcDate, tz);

  // Format in the target timezone
  return formatTz(zonedDt, formatStr, { timeZone: tz });
}

/**
 * Formats a date for API calls in UTC ISO format with timezone (YYYY-MM-DDTHH:mm:ssZ)
 * For date-only inputs, sets time to 00:00:00 UTC
 */
export function formatDateForAPI(input: string | Date | null | undefined): string | undefined {
  if (!input) {
    return undefined;
  }

  let date: Date;

  if (typeof input === "string") {
    // If it's a date-only string (YYYY-MM-DD), treat it as UTC midnight
    if (/^\d{4}-\d{2}-\d{2}$/.test(input)) {
      date = new Date(`${input}T00:00:00Z`);
    } else {
      date = new Date(input);
    }
  } else {
    // Convert to UTC ISO format with timezone
    date = new Date(input.getTime() - input.getTimezoneOffset() * 60000);
  }

  if (!isValid(date)) {
    return undefined;
  }

  return date.toISOString();
}

/**
 * Formats a boolean or string boolean value to "Yes" or "No"
 */
export function formatYesNoBoolean(value: "true" | "false" | "" | boolean | undefined): string | undefined {
  if (typeof value === "boolean") {
    return value ? "Yes" : "No";
  }

  if (typeof value === "string") {
    return value === "true" ? "Yes" : "No";
  }

  return undefined;
}
